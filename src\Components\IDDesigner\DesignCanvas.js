import React, { useRef, useEffect, useState } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { Stage, Layer, Rect, Line, Group } from 'react-konva';
import { Box } from '@mui/material';

import DesignElement from './Elements/DesignElement';
import SelectionBox from './SelectionBox';
import Ruler from './Ruler';

import {
  updateElement,
  selectElement,
  selectMultipleElements,
  clearSelection,
  updateCanvasConfig,
  togglePropertyPanel,
} from '../../redux/idDesignerSlice';
import { fromPixels, getGridSizeInPixels } from '../../utils/unitConversion';

const DesignCanvas = () => {
  const dispatch = useDispatch();
  const stageRef = useRef();
  const [stageSize, setStageSize] = useState({ width: 800, height: 600 });
  const [selectionRect, setSelectionRect] = useState(null);
  const [isDragging, setIsDragging] = useState(false);

  const {
    elements,
    selectedElementIds,
    canvasConfig,
    previewMode,
    showPropertyPanel,
  } = useSelector((state) => state.idDesigner);

  const rulerThickness = 30;

  // Handle window resize
  useEffect(() => {
    const handleResize = () => {
      const container = stageRef.current?.container();
      if (container) {
        const containerRect = container.getBoundingClientRect();
        setStageSize({
          width: containerRect.width,
          height: containerRect.height,
        });
      }
    };

    handleResize();
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // Grid lines
  const renderGrid = () => {
    if (!canvasConfig.showGrid) return null;

    const lines = [];
    const { width, height, unit, dpi } = canvasConfig;

    // Get grid size based on current unit
    const gridSize = getGridSizeInPixels(unit, dpi);

    // Vertical lines
    for (let i = 0; i <= width / gridSize; i++) {
      const x = i * gridSize;
      lines.push(
        <Line
          key={`v-${i}`}
          points={[x, 0, x, height]}
          stroke="#e0e0e0"
          strokeWidth={0.5}
          listening={false}
        />
      );
    }

    // Horizontal lines
    for (let i = 0; i <= height / gridSize; i++) {
      const y = i * gridSize;
      lines.push(
        <Line
          key={`h-${i}`}
          points={[0, y, width, y]}
          stroke="#e0e0e0"
          strokeWidth={0.5}
          listening={false}
        />
      );
    }

    return lines;
  };

  // Canvas background
  const renderCanvasBackground = () => {
    const { width, height, background, scale, offsetX, offsetY } = canvasConfig;
    
    return (
      <Rect
        x={offsetX}
        y={offsetY}
        width={width * scale}
        height={height * scale}
        fill={background}
        stroke="#ccc"
        strokeWidth={1}
        listening={false}
      />
    );
  };

  // Handle stage click
  const handleStageClick = (e) => {
    if (e.target === e.target.getStage()) {
      dispatch(clearSelection());
    }
  };

  // Handle element drag
  const handleElementDragMove = (elementId, newPos) => {
    const { snapToGrid, width, height, unit, dpi } = canvasConfig;

    let updates = { ...newPos };

    // Apply grid snapping if enabled
    if (snapToGrid && (newPos.x !== undefined || newPos.y !== undefined)) {
      const gridSize = getGridSizeInPixels(unit, dpi);
      if (newPos.x !== undefined) {
        updates.x = Math.round(newPos.x / gridSize) * gridSize;
      }
      if (newPos.y !== undefined) {
        updates.y = Math.round(newPos.y / gridSize) * gridSize;
      }
    }

    // Constrain elements within canvas bounds
    const element = elements.find(el => el.id === elementId);
    if (element) {
      if (updates.x !== undefined) {
        updates.x = Math.max(0, Math.min(width - element.width, updates.x));
      }
      if (updates.y !== undefined) {
        updates.y = Math.max(0, Math.min(height - element.height, updates.y));
      }
    }

    dispatch(updateElement({
      id: elementId,
      properties: updates,
    }));
  };

  // Handle element selection
  const handleElementClick = (elementId, e) => {
    e.cancelBubble = true;
    const multiSelect = e.evt.shiftKey || e.evt.ctrlKey || e.evt.metaKey;
    dispatch(selectElement({ id: elementId, multiSelect }));

    // Auto-open property panel when element is selected
    if (!showPropertyPanel) {
      dispatch(togglePropertyPanel());
    }
  };

  // Handle mouse down for selection rectangle
  const handleMouseDown = (e) => {
    if (e.target !== e.target.getStage()) return;
    
    const pos = e.target.getStage().getPointerPosition();
    setSelectionRect({
      x: pos.x,
      y: pos.y,
      width: 0,
      height: 0,
    });
    setIsDragging(true);
  };

  // Handle mouse move for selection rectangle
  const handleMouseMove = (e) => {
    if (!isDragging || !selectionRect) return;

    const pos = e.target.getStage().getPointerPosition();
    setSelectionRect({
      ...selectionRect,
      width: pos.x - selectionRect.x,
      height: pos.y - selectionRect.y,
    });
  };

  // Handle mouse up for selection rectangle
  const handleMouseUp = () => {
    if (!isDragging || !selectionRect) return;

    setIsDragging(false);

    // Find elements within selection rectangle
    const { scale, offsetX, offsetY } = canvasConfig;
    const selectionBounds = {
      x: Math.min(selectionRect.x, selectionRect.x + selectionRect.width) - offsetX,
      y: Math.min(selectionRect.y, selectionRect.y + selectionRect.height) - offsetY,
      width: Math.abs(selectionRect.width),
      height: Math.abs(selectionRect.height),
    };

    const selectedIds = elements
      .filter((element) => {
        const elementBounds = {
          x: element.x * scale,
          y: element.y * scale,
          width: element.width * scale,
          height: element.height * scale,
        };

        return (
          elementBounds.x >= selectionBounds.x &&
          elementBounds.y >= selectionBounds.y &&
          elementBounds.x + elementBounds.width <= selectionBounds.x + selectionBounds.width &&
          elementBounds.y + elementBounds.height <= selectionBounds.y + selectionBounds.height
        );
      })
      .map((element) => element.id);

    if (selectedIds.length > 0) {
      dispatch(selectMultipleElements(selectedIds));
    }

    setSelectionRect(null);
  };



  // Handle wheel for zoom - keep canvas centered
  const handleWheel = (e) => {
    e.evt.preventDefault();

    const stage = e.target.getStage();
    const oldScale = canvasConfig.scale;

    const newScale = e.evt.deltaY > 0 ? oldScale * 0.9 : oldScale * 1.1;
    const clampedScale = Math.max(0.1, Math.min(3, newScale));

    // Keep canvas centered during zoom
    dispatch(updateCanvasConfig({
      scale: clampedScale,
      offsetX: 0,
      offsetY: 0
    }));

    // Update stage position to keep canvas centered
    stage.position({
      x: (stageSize.width - canvasConfig.width * clampedScale) / 2,
      y: (stageSize.height - canvasConfig.height * clampedScale) / 2
    });
  };

  return (
    <Box
      sx={{
        flex: 1,
        overflow: 'hidden',
        position: 'relative',
        cursor: isDragging ? 'crosshair' : 'default',
        bgcolor: '#fafafa',
        display: 'flex',
        flexDirection: 'column',
      }}
    >
      {/* Rulers and Canvas Container */}
      <Box
        sx={{
          display: 'flex',
          position: 'relative',
        }}
      >
        {/* Top-left corner (empty space for rulers intersection) */}
        {canvasConfig.showRulers && (
          <Box
            sx={{
              width: rulerThickness,
              height: rulerThickness,
              backgroundColor: '#f5f5f5',
              borderRight: '1px solid #ddd',
              borderBottom: '1px solid #ddd',
              flexShrink: 0,
            }}
          />
        )}

        {/* Horizontal Ruler */}
        {canvasConfig.showRulers && (
          <Box sx={{ flex: 1 }}>
            <Ruler
              orientation="horizontal"
              length={stageSize.width - rulerThickness}
              scale={canvasConfig.scale}
              offset={canvasConfig.offsetX}
              unit={canvasConfig.unit}
              dpi={canvasConfig.dpi}
              thickness={rulerThickness}
              canvasWidth={canvasConfig.width}
              canvasHeight={canvasConfig.height}
              stageWidth={stageSize.width - rulerThickness}
              stageHeight={stageSize.height - rulerThickness}
            />
          </Box>
        )}
      </Box>

      {/* Main content area with vertical ruler and canvas */}
      <Box
        sx={{
          flex: 1,
          display: 'flex',
          overflow: 'hidden',
        }}
      >
        {/* Vertical Ruler */}
        {canvasConfig.showRulers && (
          <Box sx={{ flexShrink: 0 }}>
            <Ruler
              orientation="vertical"
              length={stageSize.height - rulerThickness}
              scale={canvasConfig.scale}
              offset={canvasConfig.offsetY}
              unit={canvasConfig.unit}
              dpi={canvasConfig.dpi}
              thickness={rulerThickness}
              canvasWidth={canvasConfig.width}
              canvasHeight={canvasConfig.height}
              stageWidth={stageSize.width - rulerThickness}
              stageHeight={stageSize.height - rulerThickness}
            />
          </Box>
        )}

        {/* Canvas Area */}
        <Box
          sx={{
            flex: 1,
            position: 'relative',
            overflow: 'hidden',
            display: 'flex',
            alignItems: 'flex-start',
            justifyContent: 'flex-start',
            paddingTop: 2,
            paddingLeft: 2,
          }}
        >
          <Stage
            ref={stageRef}
            width={stageSize.width - (canvasConfig.showRulers ? rulerThickness : 0)}
            height={stageSize.height - (canvasConfig.showRulers ? rulerThickness : 0)}
            scaleX={canvasConfig.scale}
            scaleY={canvasConfig.scale}
            x={canvasConfig.showRulers ? rulerThickness + 20 : 20}
            y={canvasConfig.showRulers ? rulerThickness + 20 : 20}
            onClick={handleStageClick}
            onMouseDown={handleMouseDown}
            onMouseMove={handleMouseMove}
            onMouseUp={handleMouseUp}
            onWheel={handleWheel}
            draggable={false}
          >
        <Layer>
          {/* Grid */}
          {renderGrid()}

          {/* Canvas Background */}
          {renderCanvasBackground()}

          {/* Clipping Group for Elements */}
          <Group
            clipX={0}
            clipY={0}
            clipWidth={canvasConfig.width}
            clipHeight={canvasConfig.height}
          >
            {/* Design Elements */}
            {[...elements]
              .sort((a, b) => a.zIndex - b.zIndex)
              .map((element) => (
                <DesignElement
                  key={element.id}
                  element={element}
                  isSelected={selectedElementIds.includes(element.id)}
                  isPreviewMode={previewMode}
                  onDragMove={(newPos) => handleElementDragMove(element.id, newPos)}
                  onClick={(e) => handleElementClick(element.id, e)}
                />
              ))}
          </Group>

          {/* Selection Rectangle */}
          {selectionRect && (
            <Rect
              x={selectionRect.x}
              y={selectionRect.y}
              width={selectionRect.width}
              height={selectionRect.height}
              fill="rgba(79, 38, 131, 0.05)"
              stroke="rgba(79, 38, 131, 0.6)"
              strokeWidth={1}
              shadowColor="rgba(79, 38, 131, 0.15)"
              shadowBlur={4}
              shadowOffset={{ x: 0, y: 2 }}
              listening={false}
            />
          )}
        </Layer>
        </Stage>

        {/* Selection Box for multi-select */}
        {selectedElementIds.length > 1 && (
          <SelectionBox
            elements={elements.filter(el => selectedElementIds.includes(el.id))}
            canvasConfig={canvasConfig}
          />
        )}
        </Box>
      </Box>
    </Box>
  );
};

export default DesignCanvas;
