import React from 'react';
import { useSelector, useDispatch } from 'react-redux';
import {
  Box,
  Typography,
  TextField,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  Slider,
  Switch,
  FormControlLabel,
  Divider,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Button,
  IconButton,
} from '@mui/material';
import {
  ExpandMore as ExpandMoreIcon,
  Delete as DeleteIcon,
  ContentCopy as DuplicateIcon,
  Lock as LockIcon,
  LockOpen as UnlockIcon,
  Visibility as VisibilityIcon,
  VisibilityOff as VisibilityOffIcon,
  RotateRight as RotateIcon,
  FormatAlignLeft as AlignLeftIcon,
  FormatAlignCenter as AlignCenterIcon,
  FormatAlignRight as AlignRightIcon,
  FormatAlignJustify as AlignJustifyIcon,
  FormatBold as BoldIcon,
  FormatItalic as ItalicIcon,
  FormatUnderlined as UnderlineIcon,
  FormatStrikethrough as StrikethroughIcon,
  CreditCard as CardIcon,
  Badge as BadgeIcon,
  Rectangle as CustomIcon,
} from '@mui/icons-material';

import {
  updateElement,
  deleteElement,
  duplicateElement,
  moveElementToFront,
  moveElementToBack,
  updateCanvasConfig,
  setCanvasUnit,
  toggleRulers,
  setCardType,
} from '../../redux/idDesignerSlice';
import { UNITS, fromPixels, toPixels, formatWithUnit, getGridSizeInPixels } from '../../utils/unitConversion';

const PropertyPanel = () => {
  const dispatch = useDispatch();
  const { elements, selectedElementIds, canvasConfig } = useSelector((state) => state.idDesigner);

  const selectedElements = elements.filter(el => selectedElementIds.includes(el.id));
  const selectedElement = selectedElements.length === 1 ? selectedElements[0] : null;

  const handlePropertyChange = (property, value) => {
    selectedElementIds.forEach(id => {
      dispatch(updateElement({
        id,
        properties: { [property]: value }
      }));
    });
  };

  const handleDelete = () => {
    selectedElementIds.forEach(id => {
      dispatch(deleteElement(id));
    });
  };

  const handleDuplicate = () => {
    if (selectedElement) {
      dispatch(duplicateElement(selectedElement.id));
    }
  };

  const renderCanvasProperties = () => (
    <Accordion defaultExpanded={selectedElementIds.length === 0}>
      <AccordionSummary expandIcon={<ExpandMoreIcon />}>
        <Typography variant="subtitle2">Canvas Settings</Typography>
      </AccordionSummary>
      <AccordionDetails>
        <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
          <Box sx={{ display: 'flex', gap: 1 }}>
            <TextField
              label={`Width (${canvasConfig.unit === UNITS.INCHES ? '"' : 'mm'})`}
              type="number"
              value={fromPixels(canvasConfig.width, canvasConfig.unit, canvasConfig.dpi).toFixed(2)}
              onChange={(e) => {
                const pixels = toPixels(parseFloat(e.target.value), canvasConfig.unit, canvasConfig.dpi);
                dispatch(updateCanvasConfig({ width: pixels }));
              }}
              size="small"
              sx={{ flex: 1 }}
            />
            <TextField
              label={`Height (${canvasConfig.unit === UNITS.INCHES ? '"' : 'mm'})`}
              type="number"
              value={fromPixels(canvasConfig.height, canvasConfig.unit, canvasConfig.dpi).toFixed(2)}
              onChange={(e) => {
                const pixels = toPixels(parseFloat(e.target.value), canvasConfig.unit, canvasConfig.dpi);
                dispatch(updateCanvasConfig({ height: pixels }));
              }}
              size="small"
              sx={{ flex: 1 }}
            />
          </Box>

          <Box sx={{ display: 'flex', gap: 1, alignItems: 'center' }}>
            <FormControl size="small" sx={{ flex: 1 }}>
              <InputLabel>Units</InputLabel>
              <Select
                value={canvasConfig.unit}
                onChange={(e) => dispatch(setCanvasUnit(e.target.value))}
                label="Units"
              >
                <MenuItem value={UNITS.INCHES}>Inches</MenuItem>
                <MenuItem value={UNITS.MILLIMETERS}>Millimeters</MenuItem>
              </Select>
            </FormControl>
            <FormControlLabel
              control={
                <Switch
                  checked={canvasConfig.showRulers}
                  onChange={() => dispatch(toggleRulers())}
                />
              }
              label="Rulers"
            />
          </Box>

          <TextField
            label="Background Color"
            type="color"
            value={canvasConfig.background}
            onChange={(e) => dispatch(updateCanvasConfig({ background: e.target.value }))}
            size="small"
          />

          <FormControlLabel
            control={
              <Switch
                checked={canvasConfig.showGrid}
                onChange={(e) => dispatch(updateCanvasConfig({ showGrid: e.target.checked }))}
              />
            }
            label="Show Grid"
          />

          <FormControlLabel
            control={
              <Switch
                checked={canvasConfig.snapToGrid}
                onChange={(e) => dispatch(updateCanvasConfig({ snapToGrid: e.target.checked }))}
              />
            }
            label="Snap to Grid"
          />

          <Box>
            <Typography variant="caption" gutterBottom>
              Grid Size: {formatWithUnit(fromPixels(getGridSizeInPixels(canvasConfig.unit, canvasConfig.dpi), canvasConfig.unit, canvasConfig.dpi), canvasConfig.unit)}
            </Typography>
            <Typography variant="caption" color="text.secondary">
              Grid spacing is automatically set based on unit system
            </Typography>
          </Box>

          {/* Predefined Card Sizes */}
          <Box>
            <Typography variant="caption" gutterBottom>
              Standard Card Sizes
            </Typography>
            <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
              <Box
                sx={{
                  display: 'flex',
                  flexDirection: 'column',
                  alignItems: 'center',
                  p: 1,
                  border: 1,
                  borderColor: canvasConfig.cardType === 'CR80 (Standard)' ? 'primary.main' : 'divider',
                  borderRadius: 1,
                  cursor: 'pointer',
                  bgcolor: canvasConfig.cardType === 'CR80 (Standard)' ? 'primary.light' : 'transparent',
                  '&:hover': { bgcolor: 'action.hover' }
                }}
                onClick={() => dispatch(setCardType({
                  cardType: 'CR80 (Standard)',
                  unit: canvasConfig.unit,
                  dpi: canvasConfig.dpi
                }))}
              >
                <CardIcon fontSize="small" />
                <Typography variant="caption">CR80</Typography>
                <Typography variant="caption" color="text.secondary">
                  3.375" × 2.125"
                </Typography>
              </Box>

              <Box
                sx={{
                  display: 'flex',
                  flexDirection: 'column',
                  alignItems: 'center',
                  p: 1,
                  border: 1,
                  borderColor: canvasConfig.cardType === 'CR79 (Government)' ? 'primary.main' : 'divider',
                  borderRadius: 1,
                  cursor: 'pointer',
                  bgcolor: canvasConfig.cardType === 'CR79 (Government)' ? 'primary.light' : 'transparent',
                  '&:hover': { bgcolor: 'action.hover' }
                }}
                onClick={() => dispatch(setCardType({
                  cardType: 'CR79 (Government)',
                  unit: canvasConfig.unit,
                  dpi: canvasConfig.dpi
                }))}
              >
                <BadgeIcon fontSize="small" />
                <Typography variant="caption">CR79</Typography>
                <Typography variant="caption" color="text.secondary">
                  3.303" × 2.051"
                </Typography>
              </Box>

              <Box
                sx={{
                  display: 'flex',
                  flexDirection: 'column',
                  alignItems: 'center',
                  p: 1,
                  border: 1,
                  borderColor: canvasConfig.cardType === 'Custom' ? 'primary.main' : 'divider',
                  borderRadius: 1,
                  cursor: 'pointer',
                  bgcolor: canvasConfig.cardType === 'Custom' ? 'primary.light' : 'transparent',
                  '&:hover': { bgcolor: 'action.hover' }
                }}
                onClick={() => dispatch(setCardType({
                  cardType: 'Custom',
                  unit: canvasConfig.unit,
                  dpi: canvasConfig.dpi
                }))}
              >
                <CustomIcon fontSize="small" />
                <Typography variant="caption">Custom</Typography>
                <Typography variant="caption" color="text.secondary">
                  4" × 3"
                </Typography>
              </Box>
            </Box>
          </Box>
        </Box>
      </AccordionDetails>
    </Accordion>
  );

  if (selectedElementIds.length === 0) {
    return (
      <Box sx={{ height: '100%', overflow: 'auto' }}>
        <Box sx={{ p: 2, borderBottom: 1, borderColor: 'divider' }}>
          <Typography variant="h6" sx={{ fontSize: '1rem', fontWeight: 600 }}>
            Canvas Properties
          </Typography>
        </Box>
        {renderCanvasProperties()}
      </Box>
    );
  }

  const renderTextProperties = () => {
    if (!selectedElement || selectedElement.type !== 'text') return null;

    return (
      <Accordion defaultExpanded>
        <AccordionSummary expandIcon={<ExpandMoreIcon />}>
          <Typography variant="subtitle2">Text Properties</Typography>
        </AccordionSummary>
        <AccordionDetails>
          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
            <TextField
              label="Text Content"
              value={selectedElement.text || ''}
              onChange={(e) => handlePropertyChange('text', e.target.value)}
              multiline
              rows={2}
              size="small"
              helperText="Use {{variable}} for dynamic content"
            />
            
            <FormControl size="small">
              <InputLabel>Font Family</InputLabel>
              <Select
                value={selectedElement.fontFamily || 'Arial'}
                onChange={(e) => handlePropertyChange('fontFamily', e.target.value)}
                label="Font Family"
              >
                <MenuItem value="Arial">Arial</MenuItem>
                <MenuItem value="Helvetica">Helvetica</MenuItem>
                <MenuItem value="Times New Roman">Times New Roman</MenuItem>
                <MenuItem value="Courier New">Courier New</MenuItem>
                <MenuItem value="Georgia">Georgia</MenuItem>
                <MenuItem value="Verdana">Verdana</MenuItem>
              </Select>
            </FormControl>

            <Box>
              <Typography variant="caption" gutterBottom>
                Font Size: {selectedElement.fontSize || 16}px
              </Typography>
              <Slider
                value={selectedElement.fontSize || 16}
                onChange={(e, value) => handlePropertyChange('fontSize', value)}
                min={8}
                max={72}
                size="small"
              />
            </Box>

            <TextField
              label="Text Color"
              type="color"
              value={selectedElement.color || '#000000'}
              onChange={(e) => handlePropertyChange('color', e.target.value)}
              size="small"
            />

            {/* Text Alignment */}
            <Box>
              <Typography variant="caption" gutterBottom>
                Text Alignment
              </Typography>
              <Box sx={{ display: 'flex', gap: 0.5 }}>
                <IconButton
                  size="small"
                  onClick={() => handlePropertyChange('textAlign', 'left')}
                  sx={{
                    bgcolor: selectedElement.textAlign === 'left' ? 'primary.light' : 'transparent',
                    color: selectedElement.textAlign === 'left' ? 'primary.contrastText' : 'inherit'
                  }}
                >
                  <AlignLeftIcon fontSize="small" />
                </IconButton>
                <IconButton
                  size="small"
                  onClick={() => handlePropertyChange('textAlign', 'center')}
                  sx={{
                    bgcolor: (selectedElement.textAlign || 'center') === 'center' ? 'primary.light' : 'transparent',
                    color: (selectedElement.textAlign || 'center') === 'center' ? 'primary.contrastText' : 'inherit'
                  }}
                >
                  <AlignCenterIcon fontSize="small" />
                </IconButton>
                <IconButton
                  size="small"
                  onClick={() => handlePropertyChange('textAlign', 'right')}
                  sx={{
                    bgcolor: selectedElement.textAlign === 'right' ? 'primary.light' : 'transparent',
                    color: selectedElement.textAlign === 'right' ? 'primary.contrastText' : 'inherit'
                  }}
                >
                  <AlignRightIcon fontSize="small" />
                </IconButton>
                <IconButton
                  size="small"
                  onClick={() => handlePropertyChange('textAlign', 'justify')}
                  sx={{
                    bgcolor: selectedElement.textAlign === 'justify' ? 'primary.light' : 'transparent',
                    color: selectedElement.textAlign === 'justify' ? 'primary.contrastText' : 'inherit'
                  }}
                >
                  <AlignJustifyIcon fontSize="small" />
                </IconButton>
              </Box>
            </Box>

            {/* Text Formatting */}
            <Box>
              <Typography variant="caption" gutterBottom>
                Text Formatting
              </Typography>
              <Box sx={{ display: 'flex', gap: 0.5 }}>
                <IconButton
                  size="small"
                  onClick={() => {
                    const currentStyle = selectedElement.fontStyle || 'normal';
                    const newStyle = currentStyle.includes('bold') ? 'normal' : 'bold';
                    handlePropertyChange('fontStyle', newStyle);
                  }}
                  sx={{
                    bgcolor: (selectedElement.fontStyle || '').includes('bold') ? 'primary.light' : 'transparent',
                    color: (selectedElement.fontStyle || '').includes('bold') ? 'primary.contrastText' : 'inherit'
                  }}
                >
                  <BoldIcon fontSize="small" />
                </IconButton>
                <IconButton
                  size="small"
                  onClick={() => {
                    const currentStyle = selectedElement.fontStyle || 'normal';
                    const newStyle = currentStyle.includes('italic') ? 'normal' : 'italic';
                    handlePropertyChange('fontStyle', newStyle);
                  }}
                  sx={{
                    bgcolor: (selectedElement.fontStyle || '').includes('italic') ? 'primary.light' : 'transparent',
                    color: (selectedElement.fontStyle || '').includes('italic') ? 'primary.contrastText' : 'inherit'
                  }}
                >
                  <ItalicIcon fontSize="small" />
                </IconButton>
                <IconButton
                  size="small"
                  onClick={() => {
                    const currentDecoration = selectedElement.textDecoration || '';
                    const newDecoration = currentDecoration.includes('underline') ? '' : 'underline';
                    handlePropertyChange('textDecoration', newDecoration);
                  }}
                  sx={{
                    bgcolor: (selectedElement.textDecoration || '').includes('underline') ? 'primary.light' : 'transparent',
                    color: (selectedElement.textDecoration || '').includes('underline') ? 'primary.contrastText' : 'inherit'
                  }}
                >
                  <UnderlineIcon fontSize="small" />
                </IconButton>
                <IconButton
                  size="small"
                  onClick={() => {
                    const currentDecoration = selectedElement.textDecoration || '';
                    const newDecoration = currentDecoration.includes('line-through') ? '' : 'line-through';
                    handlePropertyChange('textDecoration', newDecoration);
                  }}
                  sx={{
                    bgcolor: (selectedElement.textDecoration || '').includes('line-through') ? 'primary.light' : 'transparent',
                    color: (selectedElement.textDecoration || '').includes('line-through') ? 'primary.contrastText' : 'inherit'
                  }}
                >
                  <StrikethroughIcon fontSize="small" />
                </IconButton>
              </Box>
            </Box>

            <FormControlLabel
              control={
                <Switch
                  checked={selectedElement.autoResize !== false}
                  onChange={(e) => handlePropertyChange('autoResize', e.target.checked)}
                />
              }
              label="Auto-resize Font"
            />
          </Box>
        </AccordionDetails>
      </Accordion>
    );
  };

  const renderImageProperties = () => {
    if (!selectedElement || selectedElement.type !== 'image') return null;

    return (
      <Accordion defaultExpanded>
        <AccordionSummary expandIcon={<ExpandMoreIcon />}>
          <Typography variant="subtitle2">Image Properties</Typography>
        </AccordionSummary>
        <AccordionDetails>
          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
            <TextField
              label="Image URL"
              value={selectedElement.src || ''}
              onChange={(e) => handlePropertyChange('src', e.target.value)}
              size="small"
              helperText="Enter image URL or upload file"
            />
            
            <Button variant="outlined" size="small">
              Upload Image
            </Button>

            <FormControlLabel
              control={
                <Switch
                  checked={selectedElement.maintainAspectRatio !== false}
                  onChange={(e) => handlePropertyChange('maintainAspectRatio', e.target.checked)}
                />
              }
              label="Maintain Aspect Ratio"
            />
          </Box>
        </AccordionDetails>
      </Accordion>
    );
  };

  const renderQRCodeProperties = () => {
    if (!selectedElement || selectedElement.type !== 'qrcode') return null;

    return (
      <Accordion defaultExpanded>
        <AccordionSummary expandIcon={<ExpandMoreIcon />}>
          <Typography variant="subtitle2">QR Code Properties</Typography>
        </AccordionSummary>
        <AccordionDetails>
          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
            <TextField
              label="QR Code Data"
              value={selectedElement.data || ''}
              onChange={(e) => handlePropertyChange('data', e.target.value)}
              size="small"
              helperText="Use {{variable}} for dynamic content"
            />

            <FormControl size="small">
              <InputLabel>Error Correction</InputLabel>
              <Select
                value={selectedElement.errorCorrectionLevel || 'M'}
                onChange={(e) => handlePropertyChange('errorCorrectionLevel', e.target.value)}
                label="Error Correction"
              >
                <MenuItem value="L">Low (7%)</MenuItem>
                <MenuItem value="M">Medium (15%)</MenuItem>
                <MenuItem value="Q">Quartile (25%)</MenuItem>
                <MenuItem value="H">High (30%)</MenuItem>
              </Select>
            </FormControl>
          </Box>
        </AccordionDetails>
      </Accordion>
    );
  };

  const renderPositionProperties = () => {
    if (!selectedElement) return null;

    return (
      <Accordion defaultExpanded>
        <AccordionSummary expandIcon={<ExpandMoreIcon />}>
          <Typography variant="subtitle2">Position & Size</Typography>
        </AccordionSummary>
        <AccordionDetails>
          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
            <Box sx={{ display: 'flex', gap: 1 }}>
              <TextField
                label="X"
                type="number"
                value={Math.round(selectedElement.x || 0)}
                onChange={(e) => handlePropertyChange('x', parseFloat(e.target.value))}
                size="small"
                sx={{ flex: 1 }}
              />
              <TextField
                label="Y"
                type="number"
                value={Math.round(selectedElement.y || 0)}
                onChange={(e) => handlePropertyChange('y', parseFloat(e.target.value))}
                size="small"
                sx={{ flex: 1 }}
              />
            </Box>

            <Box sx={{ display: 'flex', gap: 1 }}>
              <TextField
                label="Width"
                type="number"
                value={Math.round(selectedElement.width || 0)}
                onChange={(e) => handlePropertyChange('width', parseFloat(e.target.value))}
                size="small"
                sx={{ flex: 1 }}
              />
              <TextField
                label="Height"
                type="number"
                value={Math.round(selectedElement.height || 0)}
                onChange={(e) => handlePropertyChange('height', parseFloat(e.target.value))}
                size="small"
                sx={{ flex: 1 }}
              />
            </Box>

            <Box>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
                <RotateIcon sx={{ fontSize: 16 }} />
                <Typography variant="caption">
                  Rotation: {Math.round(selectedElement.rotation || 0)}°
                </Typography>
                <IconButton
                  size="small"
                  onClick={() => handlePropertyChange('rotation', (selectedElement.rotation || 0) + 90)}
                  title="Rotate 90°"
                >
                  <RotateIcon sx={{ fontSize: 16 }} />
                </IconButton>
              </Box>
              <Slider
                value={selectedElement.rotation || 0}
                onChange={(e, value) => handlePropertyChange('rotation', value)}
                min={-180}
                max={180}
                size="small"
              />
            </Box>

            <Box>
              <Typography variant="caption" gutterBottom>
                Opacity: {Math.round((selectedElement.opacity || 1) * 100)}%
              </Typography>
              <Slider
                value={selectedElement.opacity || 1}
                onChange={(e, value) => handlePropertyChange('opacity', value)}
                min={0}
                max={1}
                step={0.1}
                size="small"
              />
            </Box>
          </Box>
        </AccordionDetails>
      </Accordion>
    );
  };

  return (
    <Box sx={{ height: '100%', overflow: 'auto' }}>
      <Box sx={{ p: 2, borderBottom: 1, borderColor: 'divider' }}>
        <Typography variant="h6" sx={{ fontSize: '1rem', fontWeight: 600 }}>
          Properties
        </Typography>
        {selectedElementIds.length > 1 && (
          <Typography variant="caption" color="text.secondary">
            {selectedElementIds.length} elements selected
          </Typography>
        )}
      </Box>

      {/* Element Actions */}
      <Box sx={{ p: 2, borderBottom: 1, borderColor: 'divider' }}>
        <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
          <IconButton
            size="small"
            onClick={handleDuplicate}
            disabled={selectedElementIds.length !== 1}
            title="Duplicate"
          >
            <DuplicateIcon />
          </IconButton>
          
          <IconButton
            size="small"
            onClick={handleDelete}
            color="error"
            title="Delete"
          >
            <DeleteIcon />
          </IconButton>

          <IconButton
            size="small"
            onClick={() => handlePropertyChange('locked', !selectedElement?.locked)}
            title={selectedElement?.locked ? 'Unlock' : 'Lock'}
          >
            {selectedElement?.locked ? <LockIcon /> : <UnlockIcon />}
          </IconButton>

          <IconButton
            size="small"
            onClick={() => handlePropertyChange('visible', !selectedElement?.visible)}
            title={selectedElement?.visible ? 'Hide' : 'Show'}
          >
            {selectedElement?.visible !== false ? <VisibilityIcon /> : <VisibilityOffIcon />}
          </IconButton>
        </Box>

        <Box sx={{ display: 'flex', gap: 1, mt: 1 }}>
          <Button
            size="small"
            variant="outlined"
            onClick={() => selectedElement && dispatch(moveElementToFront(selectedElement.id))}
            disabled={!selectedElement}
          >
            To Front
          </Button>
          <Button
            size="small"
            variant="outlined"
            onClick={() => selectedElement && dispatch(moveElementToBack(selectedElement.id))}
            disabled={!selectedElement}
          >
            To Back
          </Button>
        </Box>
      </Box>

      {/* Dynamic Properties */}
      <Box>
        {renderPositionProperties()}
        {renderTextProperties()}
        {renderImageProperties()}
        {renderQRCodeProperties()}
        {renderCanvasProperties()}
      </Box>
    </Box>
  );
};

export default PropertyPanel;
